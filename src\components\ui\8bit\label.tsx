"use client";

import * as React from "react";

import * as LabelPrimitive from "@radix-ui/react-label";
import { type VariantProps, cva } from "class-variance-authority";

import { cn } from "@/lib/utils";

import { Label as ShadcnLabel } from "@/components/ui/label";

export const inputVariants = cva("", {
  variants: {},
  defaultVariants: {},
});

interface BitLabelProps
  extends React.ComponentProps<typeof LabelPrimitive.Root>,
    VariantProps<typeof inputVariants> {
  asChild?: boolean;
}

function Label({ className, ...props }: BitLabelProps) {
  return <ShadcnLabel className={cn(className, "pixelated-text")} {...props} />;
}

export { Label };
