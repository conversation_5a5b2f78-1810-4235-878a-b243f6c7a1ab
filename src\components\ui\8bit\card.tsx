import { type VariantProps, cva } from "class-variance-authority";

import { cn } from "@/lib/utils";

import {
  Card as ShadcnCard,
  CardAction as <PERSON>hadcn<PERSON>ardAction,
  CardContent as <PERSON>hadcn<PERSON>ardContent,
  CardDescription as <PERSON><PERSON>cn<PERSON>ardDescription,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CardTitle as <PERSON>hadcnCardTitle,
} from "@/components/ui/card";

export const cardVariants = cva("", {
  variants: {},
  defaultVariants: {},
});

export interface BitCardProps
  extends React.ComponentProps<"div">,
    VariantProps<typeof cardVariants> {
  asChild?: boolean;
}

function Card({ ...props }: BitCardProps) {
  const { className } = props;

  return (
    <div
      className={cn(
        "relative border-y-6 border-foreground dark:border-ring !p-0",
        className
      )}
    >
      <ShadcnCard
        {...props}
        className={cn(
          "rounded-none border-0 !w-full pixelated-text",
          className
        )}
      />

      <div
        className="absolute inset-0 border-x-6 -mx-1.5 border-foreground dark:border-ring pointer-events-none"
        aria-hidden="true"
      />
    </div>
  );
}

function CardHeader({ ...props }: BitCardProps) {
  const { className } = props;

  return (
    <ShadcnCardHeader className={cn("pixelated-text", className)} {...props} />
  );
}

function CardTitle({ ...props }: BitCardProps) {
  const { className } = props;

  return (
    <ShadcnCardTitle className={cn("pixelated-text", className)} {...props} />
  );
}

function CardDescription({ ...props }: BitCardProps) {
  const { className } = props;

  return (
    <ShadcnCardDescription
      className={cn("pixelated-text", className)}
      {...props}
    />
  );
}

function CardAction({ ...props }: BitCardProps) {
  const { className } = props;

  return (
    <ShadcnCardAction className={cn("pixelated-text", className)} {...props} />
  );
}

function CardContent({ ...props }: BitCardProps) {
  const { className } = props;

  return (
    <ShadcnCardContent className={cn("pixelated-text", className)} {...props} />
  );
}

function CardFooter({ ...props }: BitCardProps) {
  const { className } = props;

  return (
    <ShadcnCardFooter
      data-slot="card-footer"
      className={cn("pixelated-text", className)}
      {...props}
    />
  );
}

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardAction,
  CardDescription,
  CardContent,
};
